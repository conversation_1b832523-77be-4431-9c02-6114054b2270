<template>
  <NuxtLink :to="`/song/${song.id}`">
    <Card
      class="hover:ring-primary-500 hover:ring-1 cursor-pointer"
      @click="$emit('click')"
    >
      <div class="flex items-start gap-2">
        <div>
          <Image
            class="w-12 rounded-md object-cover"
            :src="song.thumbnail"
            :alt="song.title"
          >
            <template #error>
              <div class="flex h-12 w-12 items-center justify-center rounded-md bg-white">
                <Icon
                  name="ph:music-notes"
                  class="size-8"
                />
              </div>
            </template>
          </Image>
        </div>
        <div class="flex-1">
          <h3 class="text-secondary font-bold">
            {{ song.title }}
          </h3>
          <NuxtLink
            :to="`/artist/${song.artist_slug}`"
            class="text-muted text-sm hover:underline"
          >
            {{ song.artist }}
          </NuxtLink>
        </div>
      </div>
    </Card>
  </NuxtLink>
</template>

<script setup lang="ts">
import { Image } from '#components'

const props = defineProps<{
  song: SongItem | SongSmallItem
}>()

defineEmits<{
  click: []
}>()
</script>
