<template>
  <div class="song-lyrics">
    <!-- Song Header -->
    <Card class="mb-6 p-2 lg:p-4">
      <div class="flex flex-col gap-4 md:flex-row">
        <Image
          class="h-[150px] w-full rounded-md object-cover md:h-auto md:w-[80px]"
          :src="song.thumbnail"
          :alt="song.title"
        >
          <template #error>
            <div class="flex size-[80px] items-center justify-center rounded-md bg-white/60">
              <Icon
                name="ph:music-notes"
                class="size-[50px]"
              />
            </div>
          </template>
        </Image>
        <div>
          <h1 class="text-secondary mb-1 text-2xl font-bold md:text-3xl">
            {{ song.title }}
          </h1>
          <NuxtLink
            :to="`/artist/${song.artist_slug}`"
            class="text-muted text-lg hover:underline"
          >
            {{ song.artist }}
          </NuxtLink>
          <div class="mt-2">
            <Badge
              color="secondary"
              class="min-w-10 cursor-pointer justify-center rounded-full"
              :label="song.genreThai"
            />
          </div>
        </div>
      </div>
      <h3 class="text-secondary mt-4 text-lg font-semibold">
        คอร์ดที่ใช้
      </h3>
      <div class="flex flex-wrap gap-2">
        <Badge
          v-for="chord in transposedChords"
          :key="chord"
          class="min-w-10 cursor-pointer justify-center rounded-full"
          @click="showChordDiagram(chord)"
        >
          {{ chord }}
        </Badge>
      </div>
    </Card>

    <!-- Controls -->
    <div class="mb-6 flex flex-col items-center justify-center gap-2 lg:flex-row lg:flex-wrap">
      <div class="flex items-center gap-2">
        <Button
          variant="soft"
          color="neutral"
          size="sm"
          @click="toggleChords"
        >
          {{ showChords ? 'ซ่อนคอร์ด' : 'แสดงคอร์ด' }}
        </Button>

        <Button
          color="success"
          size="sm"
          @click="toggleAutoScroll"
        >
          {{ autoScroll ? 'หยุด Auto Scroll' : 'เริ่ม Auto Scroll' }}
        </Button>
        <div class="flex items-center gap-2">
          <label class="text-sm text-white">ขนาดตัวอักษร :</label>
          <Select
            v-model="fontSize"
            :items="[
              {
                label: 'เล็ก',
                value: 'text-sm',
              },
              {
                label: 'ปกติ',
                value: 'text-base',
              },
              {
                label: 'ใหญ่',
                value: 'text-lg',
              },
              {
                label: 'ใหญ่มาก',
                value: 'text-xl',
              },
            ]"
            class="w-24"
          />
        </div>
      </div>

      <div class="flex flex-col items-center justify-center gap-2 lg:flex-row lg:flex-wrap">
        <div class="flex items-center gap-2">
          <span class="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
            Key: {{ currentKey }}
          </span>
          <span
            class="rounded-full bg-yellow-100 px-3 py-1 text-sm font-medium text-yellow-800"
          >
            Tuning: {{ currentTuning }}
          </span>
        </div>

        <div class="flex items-center gap-2">
          <Button
            size="sm"
            square
            class="rounded-full bg-purple-100 text-sm font-medium text-purple-800 hover:bg-purple-200"
            title="ลดคีย์"
            icon="lucide:minus"
            @click="transposeDown"
          />
          <span class="text-xs text-white">Transpose</span>
          <Button
            size="sm"
            square
            class="rounded-full bg-purple-100 text-sm font-medium text-purple-800 hover:bg-purple-200"
            title="เพิ่มคีย์"
            icon="lucide:plus"
            @click="transposeUp"
          />
          <Button
            size="sm"
            class="rounded-full bg-gray-100 font-medium text-gray-600 hover:bg-gray-200"
            title="รีเซ็ต"
            @click="resetTranspose"
          >
            Reset
          </Button>
        </div>
      </div>
    </div>

    <div class="flex flex-col gap-4 lg:flex-row">
      <div class="flex-1">
        <div
          class="space-y-6 rounded-lg bg-white/80 py-6 text-center"
          :class="fontSize"
        >
          <div
            v-for="(section, sectionIndex) in song.lyrics"
            :key="sectionIndex"
            class="space-y-1 px-2 md:px-6"
          >
            <div
              v-for="(lineGroup, groupIndex) in getLineGroups(section.lines)"
              :key="groupIndex"
            >
              <div class="flex flex-col justify-center gap-2 sm:flex-row">
                <template
                  v-for="(line, lineIndex) in lineGroup"
                  :key="`${groupIndex}-${lineIndex}`"
                >
                  <template
                    v-if="showChords"
                  >
                    <p
                      v-if="line.chordsWithPositions?.length"
                      class="leading-10 text-gray-800"
                      @click="handleChordClick"
                      v-html="renderInlineChords(line, section.section)"
                    />
                    <p
                      v-else-if="line.chords?.length && !line.text"
                      class="chords-only mb-2"
                    >
                      <span
                        v-for="(chord, chordIndex) in line.chords"
                        :key="chordIndex"
                        class="c_chord mr-4 cursor-pointer font-bold text-blue-600 hover:text-blue-800"
                        :data-chord="transposeChord(chord)"
                        @click="showChordDiagram(transposeChord(chord))"
                      >
                        {{ transposeChord(chord) }}
                      </span>
                    </p>
                    <p
                      v-else
                      class="cv-th relative leading-10 text-gray-800"
                    >
                      {{ line.text || '' }}
                    </p>
                  </template>
                  <template v-else>
                    <!-- แสดงเฉพาะเนื้อเพลง -->
                    <div
                      v-if="!section.section.startsWith('IN')"
                      class="lyrics-line leading-relaxed text-gray-800"
                    >
                      {{ line.text || (line.chords?.length ? `[${line.chords.join(' - ')}]` : '') }}
                    </div>
                  </template>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lg:w-64">
        <Card class="mb-4 text-center text-lg font-bold">
          จาก {{ song.artist }}
        </Card>
        <Loader
          :loading="artistSong.fetch.status.isLoading"
        >
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-1">
            <SongCard
              v-for="song in artistSong.fetch.items"
              :key="song.id"
              :song="song"
            />
          </div>
          <div class="mt-4 flex justify-center">
            <Button
              block
              :to="`/artist/${song.artist_slug}`"
              icon="lucide:music"
            >
              เพลงทั้งหมดของ {{ song.artist }}
            </Button>
          </div>
        </Loader>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '#components'
import ChordModal from './ChordModal.vue'

// Props
const props = defineProps<{
  song: SongItem
}>()

// Reactive data
const showChords = ref(true)
const autoScroll = ref(false)
const fontSize = ref('text-base')
const overlay = useOverlay()
const chordModal = overlay.create(ChordModal)
const transposeSteps = ref(0)
const artistSong = useSongByArtistLoader()

artistSong.fetchSetLoading()
onMounted(() => {
  artistSong.fetchPage(1, props.song.artist_slug, {
    params: {
      limit: 14,
    },
  })
})

// Computed
const currentKey = computed(() => {
  return transposeChord(props.song.key)
})

const tuning = computed(() => {
  if (props.song.tuning === 'Original Key') {
    return 0
  } else if (props.song.tuning === 'Tune down 1/2 to Eb') {
    return 1
  } else if (props.song.tuning === 'Tune down 1 to D') {
    return 2
  } else if (props.song.tuning === 'CAPO 9') {
    return 3
  } else if (props.song.tuning === 'CAPO 8') {
    return 4
  } else if (props.song.tuning === 'CAPO 7') {
    return 5
  } else if (props.song.tuning === 'CAPO 6') {
    return 6
  } else if (props.song.tuning === 'CAPO 5') {
    return 7
  } else if (props.song.tuning === 'CAPO 4') {
    return 8
  } else if (props.song.tuning === 'CAPO 3') {
    return 9
  } else if (props.song.tuning === 'CAPO 2') {
    return 10
  } else if (props.song.tuning === 'CAPO 1') {
    return 11
  }

  return 0
})

const currentTuning = computed(() => {
  const now_tune = tuning.value + transposeSteps.value

  if (now_tune === 0 || now_tune === 12) {
    return 'Original Key'
  } else if (now_tune === 1 || now_tune === -11) {
    return 'Tune down 1/2 to Eb'
  } else if (now_tune === 2 || now_tune === -10) {
    return 'Tune down 1 to D'
  } else if (now_tune === 3 || now_tune === -9) {
    return 'CAPO 9'
  } else if (now_tune === 4 || now_tune === -8) {
    return 'CAPO 8'
  } else if (now_tune === 5 || now_tune === -7) {
    return 'CAPO 7'
  } else if (now_tune === 6 || now_tune === -6) {
    return 'CAPO 6'
  } else if (now_tune === 7 || now_tune === -5) {
    return 'CAPO 5'
  } else if (now_tune === 8 || now_tune === -4) {
    return 'CAPO 4'
  } else if (now_tune === 9 || now_tune === -3) {
    return 'CAPO 3'
  } else if (now_tune === 10 || now_tune === -2) {
    return 'CAPO 2'
  } else if (now_tune === 11 || now_tune === -1) {
    return 'CAPO 1'
  } else if (now_tune === 13 || now_tune === 1) {
    return 'Tune down 1/2 to Eb'
  } else if (now_tune === 14 || now_tune === 2) {
    return 'Tune down 1 to D'
  } else if (now_tune === 15 || now_tune === 3) {
    return 'CAPO 9'
  } else if (now_tune === 16 || now_tune === 4) {
    return 'CAPO 8'
  } else if (now_tune === 17 || now_tune === 5) {
    return 'CAPO 7'
  } else if (now_tune === 18 || now_tune === 6) {
    return 'CAPO 6'
  } else if (now_tune === 19 || now_tune === 7) {
    return 'CAPO 5'
  } else if (now_tune === 20 || now_tune === 8) {
    return 'CAPO 4'
  } else if (now_tune === 21 || now_tune === 9) {
    return 'CAPO 3'
  } else if (now_tune === 22 || now_tune === 10) {
    return 'CAPO 2'
  } else if (now_tune === 23 || now_tune === 11) {
    return 'CAPO 1'
  } else if (now_tune === 24 || now_tune === 12) {
    return 'Original Key'
  }

  return 'Unknown'
})

const transposedChords = computed(() => {
  return props.song.chords.map((chord) => transposeChord(chord))
})

// Methods
const getLineGroups = (lines: LyricLine[]): LyricLine[][] => {
  const groups: LyricLine[][] = []

  for (let i = 0; i < lines.length; i += 2) {
    const group = lines.slice(i, i + 2)

    groups.push(group)
  }

  return groups
}

const toggleChords = () => {
  showChords.value = !showChords.value
}

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value

  if (autoScroll.value) {
    startAutoScroll()
  } else {
    stopAutoScroll()
  }
}

const showChordDiagram = (chord: string) => {
  chordModal.open({
    chord,
  })
}

// Transpose functions
const chromaticScale = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
const flatScale = ['C', 'Db', 'D', 'Eb', 'E', 'F', 'Gb', 'G', 'Ab', 'A', 'Bb', 'B']

const transposeChord = (chord: string): string => {
  if (!chord || transposeSteps.value === 0) return chord

  const chordMatch = chord.match(/^([A-G][#b]?)(.*)$/)
  if (!chordMatch) return chord

  const [, rootNote, quality] = chordMatch

  let noteIndex = chromaticScale.indexOf(rootNote)

  if (noteIndex === -1) {
    noteIndex = flatScale.indexOf(rootNote)
  }

  if (noteIndex === -1) return chord

  let newIndex = (noteIndex + transposeSteps.value) % 12
  if (newIndex < 0) newIndex += 12

  const useFlats = rootNote.includes('b') || ['F', 'Bb', 'Eb', 'Ab', 'Db', 'Gb'].includes(rootNote)
  const newNote = useFlats ? flatScale[newIndex] : chromaticScale[newIndex]

  return newNote + quality
}

const transposeUp = () => {
  transposeSteps.value = (transposeSteps.value + 1) % 12
}

const transposeDown = () => {
  transposeSteps.value = (transposeSteps.value - 1 + 12) % 12
}

const resetTranspose = () => {
  transposeSteps.value = 0
}

// Render functions

const renderInlineChords = (line: LyricLine, section: string): string => {
  if (!line.text) {
    return line.chords?.length
      ? line.chords.map((chord) => `<span class="c_chord relative inline-block font-bold text-primary cursor-pointer transition-colors duration-200 mx-px hover:text-primary-800" data-chord="${transposeChord(chord)}">${transposeChord(chord)}</span>`).join('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;')
      : ''
  }

  if (!line.chordsWithPositions?.length) {
    return line.text
  }

  const text = line.text
  const sortedChords = [...line.chordsWithPositions].sort((a, b) => a.position - b.position)

  let result = ''
  let lastPosition = 0

  sortedChords.forEach((chordPos) => {
    const {
      chord, position,
    } = chordPos

    const transposedChord = transposeChord(chord)

    // เพิ่มข้อความก่อนคอร์ด
    const textBefore = text.slice(lastPosition, position)

    result += textBefore

    if (section.startsWith('IN')) {
    // เพิ่มคอร์ดแบบ inline ในตำแหน่งที่เหมาะสม (คอร์ดจะแสดงด้านบนผ่าน CSS)
      result += `<span class="inline-block font-bold text-primary cursor-pointer transition-colors duration-200 mx-px hover:text-primary-800 mr-2" data-chord="${transposedChord}">${transposedChord}</span>`
    } else {
      //
    // เพิ่มคอร์ดแบบ inline ในตำแหน่งที่เหมาะสม (คอร์ดจะแสดงด้านบนผ่าน CSS)
      result += `<span class="relative inline-block font-bold text-primary cursor-pointer transition-colors duration-200 mx-px hover:text-primary-800" style="
    left: 0px;
    width: 25px;
    top: -20px;
    margin-left: -25px;
    text-align: right;
    word-wrap: normal;" data-chord="${transposedChord}">${transposedChord}</span>`
    }

    lastPosition = position
  })

  // เพิ่มข้อความที่เหลือ
  const remainingText = text.slice(lastPosition)

  result += remainingText

  return result
}

const handleChordClick = (event: Event) => {
  const target = event.target as HTMLElement

  if (target && target.hasAttribute('data-chord')) {
    const chord = target.getAttribute('data-chord')

    if (chord) {
      showChordDiagram(chord)
    }
  }
}

// Auto scroll
let scrollInterval: NodeJS.Timeout | null = null

const startAutoScroll = () => {
  scrollInterval = setInterval(() => {
    window.scrollBy(0, 1)
  }, 100)
}

const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

// Cleanup
onUnmounted(() => {
  stopAutoScroll()
})
</script>
