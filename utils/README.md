# Song Extraction Utilities

This module provides utilities for extracting song data from HTML content, particularly from dochord.com.

## Functions

### `extractKeyFromContent(content: string): string`
Extracts the musical key from content using Thai and English patterns.

```typescript
const content = 'คีย์: Am'
const key = extractKeyFromContent(content) // Returns 'Am'
```

### `extractCapoFromContent(content: string): number`
Extracts capo position from content.

```typescript
const content = 'Capo: 3'
const capo = extractCapoFromContent(content) // Returns 3
```

### `extractChordsFromContent(content: string): string[]`
Extracts all unique chords from content, filtering out duplicates and subchords.

```typescript
const content = 'C G Am F Cmaj7'
const chords = extractChordsFromContent(content) // Returns ['Cmaj7', 'G', 'Am', 'F']
```

### `extractLyricsFromContent(content: string): SongData['lyrics']`
Extracts structured lyrics with sections and chord information from HTML content.

```typescript
const htmlContent = '<div>VERSE: C G Am F<br>Hello world</div>'
const lyrics = extractLyricsFromContent(htmlContent)
```

### `extractFromWebContent(webContent: string): WebScrapedData`
Comprehensive extraction from web scraped content including chord positions.

```typescript
const webContent = '<div>Key G<br>C Hello G world</div>'
const data = extractFromWebContent(webContent)
// Returns { lyrics: [...], chords: ['C', 'G'], key: 'G' }
```

### `removeChords(text: string): string`
Removes chord notations from text while preserving lyrics.

```typescript
const text = 'C Hello G world Am'
const clean = removeChords(text) // Returns 'Hello world'
```

### `removeChordsFromLyrics(lyrics: SongData['lyrics']): SongData['lyrics']`
Removes chords from lyrics structure while preserving the data structure.

```typescript
const lyrics = [{ section: 'Verse', lines: [{ text: 'C Hello', chords: ['C'] }] }]
const clean = removeChordsFromLyrics(lyrics)
// Returns [{ section: 'Verse', lines: [{ text: 'Hello', chords: ['C'] }] }]
```

## Usage in API

The refactored API endpoint now uses these utilities:

```typescript
import {
  extractKeyFromContent,
  extractCapoFromContent,
  extractChordsFromContent,
  extractLyricsFromContent,
  extractFromWebContent,
  removeChordsFromLyrics,
} from '~/utils/songExtraction'

// In your API handler
const key = extractKeyFromContent(content)
const capo = extractCapoFromContent(content)
const chords = extractChordsFromContent(content)
const lyrics = removeChordsFromLyrics(extractLyricsFromContent(content))
```

## Testing

Run the unit tests:

```bash
bun test test/utils/songExtraction.test.ts
```

Run integration tests:

```bash
bun test test/api/songs.integration.test.ts
```
