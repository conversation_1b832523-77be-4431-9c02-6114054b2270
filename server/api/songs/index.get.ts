import * as cheerio from 'cheerio'
import axios from 'axios'

interface SongData {
  id: string
  title: string
  artist: string
  artist_slug: string
  thumbnail: string
}

interface SongSection {
  title: string
  description: string
  dateRange: string
  songs: SongData[]
}

const parseSongDataFromContainer = ($: cheerio.CheerioAPI, container: cheerio.Element): SongData[] => {
  const songs: SongData[] = []

  $(container).find('div[style*="clear:both"]').each((_, songDiv) => {
    const $songDiv = $(songDiv)

    const titleLink = $songDiv.find('.h3-popular-song a')
    const artistLink = $songDiv.find('.h4-popular-song a')
    const imageElement = $songDiv.find('img')

    if (titleLink.length && artistLink.length && imageElement.length) {
      const href = titleLink.attr('href') || ''
      const idMatch = href.match(/\/(\d+)\//)
      const id = idMatch ? idMatch[1] : ''

      const title = titleLink.text().trim()
      const artist = artistLink.text().trim()
      const thumbnail = imageElement.attr('data-src') || imageElement.attr('src')

      // ดึง artist_slug จาก artist URL
      const artistHref = artistLink.attr('href') || ''
      const artistSlugMatch = artistHref.match(/\/artist\/([^/]+)/)
      const artist_slug = artistSlugMatch ? decodeURIComponent(artistSlugMatch[1]) : ''

      if (id && title && artist && thumbnail) {
        songs.push({
          id,
          title,
          artist,
          thumbnail,
          artist_slug,
        })
      }
    }
  })

  return songs
}

const parseSection = ($: cheerio.CheerioAPI, section: cheerio.Element): SongSection => {
  const $section = $(section)
  const title = $section.find('.section-third-title').text().trim()
  const descElement = $section.find('.section-third-desc')

  // แยก description และ date range
  const fullDesc = descElement.text().trim()
  const dateRangeElement = descElement.find('span[style*="color: forestgreen"]')
  const dateRange = dateRangeElement.text().trim()

  // ลบ date range ออกจาก description
  const description = fullDesc.replace(dateRange, '').trim()

  // รวบรวมเพลงจากทุก song-list container
  let allSongs: SongData[] = []

  $section.find('.section-third-song-list').each((_, container) => {
    const containerSongs = parseSongDataFromContainer($, container)

    allSongs = allSongs.concat(containerSongs)
  })

  return {
    title,
    description,
    dateRange,
    songs: allSongs,
  }
}

// Function หลักสำหรับ parse ทุก sections เป็น array
export const parseAllSections = (html: string): SongSection[] => {
  const $ = cheerio.load(html)
  const sections: SongSection[] = []

  $('.section-third-row').each((_, section) => {
    const parsedSection = parseSection($, section)

    // เพิ่มเฉพาะ section ที่มี title และมีเพลง
    if (parsedSection.title && parsedSection.songs.length > 0) {
      sections.push(parsedSection)
    }
  })

  return sections
}

// Function สำหรับ filter section ตาม type
export const getSectionByType = (sections: SongSection[], type: 'popular' | 'mostSearched'): SongSection | null => {
  const section = sections.find((section) => {
    if (type === 'popular') {
      return section.title.includes('คอร์ดเพลงยอดนิยม')
    } else if (type === 'mostSearched') {
      return section.title.includes('คอร์ดเพลงค้นหามากสุด')
    }

    return false
  })

  return section || null
}

// Function สำหรับ filter section ตาม title pattern
export const getSectionByTitle = (sections: SongSection[], titlePattern: string): SongSection | null => {
  const section = sections.find((section) =>
    section.title.includes(titlePattern),
  )

  return section || null
}

// Function สำหรับรวมเพลงจากทุก sections
export const getAllSongsFromSections = (sections: SongSection[]): SongData[] => {
  return sections.reduce((allSongs, section) => {
    return allSongs.concat(section.songs)
  }, [] as SongData[])
}

// Convenience functions สำหรับการใช้งานที่ง่าย
export const parsePopularSection = (html: string): SongSection | null => {
  const sections = parseAllSections(html)

  return getSectionByType(sections, 'popular')
}

export const parseMostSearchedSection = (html: string): SongSection | null => {
  const sections = parseAllSections(html)

  return getSectionByType(sections, 'mostSearched')
}

export const parsePopularSongs = (html: string): SongData[] => {
  const section = parsePopularSection(html)

  return section ? section.songs : []
}

export const parseMostSearchedSongs = (html: string): SongData[] => {
  const section = parseMostSearchedSection(html)

  return section ? section.songs : []
}

export const parseAllSongs = (html: string): SongData[] => {
  const sections = parseAllSections(html)

  return getAllSongsFromSections(sections)
}

// Function สำหรับ get section stats
export const getSectionStats = (sections: SongSection[]) => {
  return sections.map((section) => ({
    title: section.title,
    description: section.description,
    dateRange: section.dateRange,
    songCount: section.songs.length,
  }))
}

// Function สำหรับ parse เพลงใหม่ๆ (โครงสร้าง HTML แตกต่าง)
const parseNewSongs = (html: string): SongData[] => {
  const $ = cheerio.load(html)
  const songs: SongData[] = []

  $('.base-new-post-item').each((_, item) => {
    const $item = $(item)

    // ดึง ID จาก class name (เช่น item-379841)
    const itemClass = $item.attr('class') || ''
    const idMatch = itemClass.match(/item-(\d+)/)
    const id = idMatch ? idMatch[1] : ''

    // ดึงชื่อเพลงจาก title link
    const titleLink = $item.find('.base-new-post-item-content-title a')
    const title = titleLink.text().trim()

    // ดึงศิลปินจาก artist link
    const artistLink = $item.find('.base-new-post-item-content-artist a')
    const artist = artistLink.text().trim()

    // ดึง artist_slug จาก artist URL
    const artistHref = artistLink.attr('href') || ''
    const artistSlugMatch = artistHref.match(/\/artist\/([^/]+)/)
    const artist_slug = artistSlugMatch ? decodeURIComponent(artistSlugMatch[1]) : ''

    // ดึง thumbnail จาก img src
    const imageElement = $item.find('.base-new-post-item-thumbnail img')
    const thumbnail = imageElement.attr('data-src') || imageElement.attr('src')

    if (id && title && artist && thumbnail) {
      songs.push({
        id,
        title,
        artist,
        artist_slug,
        thumbnail,
      })
    }
  })

  return songs
}

// Function สำหรับ parse section เพลงใหม่ๆ พร้อม metadata
export const parseNewSongSection = (html: string): SongSection => {
  const $ = cheerio.load(html)
  const title = $('.section-new-title').text().trim()

  return {
    title: title || 'คอร์ดเพลงใหม่ๆ',
    description: 'เพลงใหม่ล่าสุดที่เพิ่มเข้ามาในระบบ',
    dateRange: '',
    songs: parseNewSongs(html),
  }
}

// Function รวมสำหรับ parse ทั้ง sections เก่าและใหม่
export const parseAllSongSections = (html: string): SongSection[] => {
  const $ = cheerio.load(html)
  const sections: SongSection[] = []

  // Parse sections เก่า (popular, most searched)
  $('.section-third-row').each((_, section) => {
    const parsedSection = parseSection($, section)

    if (parsedSection.title && parsedSection.songs.length > 0) {
      sections.push(parsedSection)
    }
  })

  // Parse section เพลงใหม่
  const newSongSection = parseNewSongSection(html)

  if (newSongSection.songs.length > 0) {
    sections.push(newSongSection)
  }

  return sections
}

// Function สำหรับ parse เฉพาะเพลงใหม่
export const parseNewSongsOnly = (html: string): SongData[] => {
  return parseNewSongs(html)
}

export default defineEventHandler(async (event) => {
  try {
    const {
      data,
    } = await axios.get('https://www.dochord.com/', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
      },
    })

    const sections = parseAllSongSections(data)

    return sections
  } catch (error) {
    console.error('Error fetching from dochord.com:', error)
    event.node.res.statusCode = 500

    return {
      code: '500',
      message: 'Failed to fetch ',
    }
  }
})
