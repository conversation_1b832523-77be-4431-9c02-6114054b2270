# Project Structure

## Root Level
- `app.vue` - Main application entry point with layout wrapper
- `nuxt.config.ts` - Nuxt configuration with SSR and Thai optimization
- `app.config.ts` - UI theme and component configuration
- `error.vue` - Global error page

## Core Directories

### `/pages` - File-based routing
- `index.vue` - Homepage
- `chords.vue` - Browse all chords with filtering
- `search.vue` - Search functionality
- `songs.vue` - Browse songs
- `/chord/[id].vue` - Individual chord detail pages
- `/song/[id].vue` - Individual song detail pages

### `/components` - Reusable Vue components
- `ChordCard.vue` - Chord preview card with diagram
- `ChordDiagram.vue` - Visual chord fingering diagram
- `SongCard.vue` - Song preview card
- `SongLyrics.vue` - Song lyrics display

### `/server/api` - Backend API routes
- Server-side API endpoints for chord and song data

### `/assets` - Static assets
- `/css` - Global stylesheets and Tailwind imports

### `/constants` - Application constants
- `site.ts` - Site metadata and configuration

### `/composables` - Vue composables
- `useRequestOptions.ts` - HTTP request utilities

## Architecture Patterns

### Component Structure
- Use `<script setup>` syntax for all components
- Define TypeScript interfaces for props
- Emit events using `defineEmits<{}>()`
- Computed properties for reactive data transformations

### Page Structure
- Each page includes SEO meta tags via `useHead()`
- Reactive data management with `ref()` and `reactive()`
- API calls in `onMounted()` lifecycle hook
- Loading states and error handling

### Naming Conventions
- Components: PascalCase (e.g., `ChordCard.vue`)
- Pages: lowercase (e.g., `chords.vue`)
- Composables: camelCase with `use` prefix
- Constants: UPPER_SNAKE_CASE

### File Organization
- Keep components focused and single-purpose
- Use TypeScript interfaces for type safety
- Separate business logic into composables when reusable
- Store configuration in dedicated const
