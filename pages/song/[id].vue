<template>
  <!-- Loading State -->
  <Loader
    :loading="song.status.value.isLoading"
  >
    <div
      v-if="song.status.value.isError"
    >
      <Icon
        name="lucide:alert-circle"
        class="mx-auto mb-4 h-16 w-16 text-red-500"
      />
      <h2 class="mb-2 text-2xl font-bold text-gray-900">
        ไม่พบเพลงที่ต้องการ
      </h2>
      <p class="mb-6 text-gray-600">
        {{ StringHelper.getError(song.status.value.errorData, 'มีบางอย่างผิดพลาด') }}
      </p>
      <NuxtLink
        to="/songs"
        class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
      >
        <Icon
          name="lucide:music"
          class="mr-2 h-4 w-4"
        />
        ดูเพลงอื่น
      </NuxtLink>
    </div>

    <SongLyrics
      v-else-if="song.data.value"
      :song="song.data.value"
    />
    <YouTubePlayer />
  </Loader>

  <!-- Error State -->
</template>

<script setup lang="ts">
const route = useRoute()
const songId = route.params.id as string

const {
  playSong,
} = useYouTubePlayer()

const song = useSongFind()

song.setLoading()
onMounted(async () => {
  song.run({
    urlBind: {
      id: songId,
    },
  })
})

useWatchTrue(() => song.status.value.isSuccess, () => {
  const songData = song.data.value!

  // Update SEO
  useHead({
    title: `${songData.title} - ${songData.artist} | Mhalong chords`,
    meta: [
      {
        name: 'description',
        content: `เนื้อเพลงและคอร์ดกีตาร์ ${songData.title} โดย ${songData.artist} - ${songData.description}`,
      },
    ],
  })

  if (songData && songData.youtubeId) {
    playSong({
      id: songData.id,
      title: songData.title,
      artist: songData.artist,
      youtubeId: songData.youtubeId,
      thumbnail: songData.thumbnail,
    })
  }
})
</script>
