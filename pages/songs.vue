<template>
  <!-- Search and Filters -->
  <div class="mb-8 rounded-lg bg-white/80 p-6 shadow-sm">
    <p class="mb-4 flex-1 text-2xl font-bold">
      ค้นหาเพลง
    </p>
    <div class="mb-4 flex flex-col gap-4 md:flex-row">
      <div class="flex-1">
        <div class="relative">
          <Input
            v-model="searchQuery"
            type="text"
            leading-icon="lucide:search"
            inputmode="search"
            placeholder="ค้นหาเพลง ศิลปิน หรือคอร์ด..."
            @keyup.enter="performSearch"
          />
        </div>
      </div>

      <button
        :disabled="search.fetch.status.isLoading"
        class="bg-secondary rounded-lg px-6 py-3 font-medium text-white transition-colors hover:bg-secondary-700 disabled:opacity-50"
        @click="performSearch"
      >
        <Icon
          v-if="search.fetch.status.isLoading"
          name="lucide:loader-2"
          class="h-5 w-5 animate-spin"
        />
        <span v-else>ค้นหา</span>
      </button>
    </div>
  </div>

  <FlexDeck
    :options="flexOptions"
    container-class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
    @pageChange="search.fetchPageChange"
    @search="search.fetchSearch"
  >
    <template #loading-state>
      <SongListSkeleton />
    </template>
    <template #default="{ row }: { row: SongSmallItem }">
      <SongCard
        :song="row"
      />
    </template>
  </FlexDeck>
</template>

<script setup lang="ts">
const currentPage = ref(1)
const searchQuery = ref('')
const currentQuery = ref('')
const search = useSongSearchLoader()

// Methods
const fetchSongs = async () => {
  search.fetchPage(currentPage.value, currentQuery.value)
}

const performSearch = async () => {
  currentQuery.value = searchQuery.value.trim()
  currentPage.value = 1
  await fetchSongs()
}

search.fetchSetLoading()
onMounted(async () => {
  const route = useRoute()
  const urlQuery = route.query.q as string

  if (urlQuery) {
    searchQuery.value = urlQuery
    currentQuery.value = urlQuery
  }

  fetchSongs()
})

const flexOptions = useFlexDeck<SongSmallItem>({
  repo: search,
})
</script>
