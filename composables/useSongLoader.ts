export interface SongSmallItem {
  id: string
  title: string
  artist: string
  artist_slug: string
  thumbnail: string
}

export interface SongSection {
  title: string
  description: string
  dateRange: string
  songs: SongSmallItem[]
}

export interface ChordPosition {
  chord: string
  position: number
}

export interface LyricLine {
  text: string
  chords: string[]
  chordsWithPositions: ChordPosition[]
}

export interface LyricSection {
  section: string
  lines: LyricLine[]
}

export interface SongItem {
  id: string
  title: string
  artist: string
  artist_slug: string
  genre: string
  genreThai: string
  difficulty: string
  difficultyThai: string
  key: string
  tuning: string
  tempo: number
  timeSignature: string
  capo: number
  strummingPattern: string
  chords: string[]
  lyrics: LyricSection[]
  tags: string[]
  description: string
  youtubeId: string
  thumbnail: string
}

export const useSongSectionsLoader = () => {
  return useListLoader<SongSection>({
    url: '/api/songs',
  })
}

export const useSongSearchLoader = () => {
  return usePageLoader<SongSmallItem>({
    baseURL: '/api/songs/search',
  })
}

export const useSongByArtistLoader = () => {
  return usePageLoader<SongSmallItem>({
    baseURL: '/api/songs/artist',
  })
}

export const useSongFind = () => {
  return useObjectLoader<SongItem>({
    url: '/api/songs/:id',
    method: 'get',
  })
}
