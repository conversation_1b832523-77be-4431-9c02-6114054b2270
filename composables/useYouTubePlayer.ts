interface Song {
  id: string
  title: string
  artist: string
  youtubeId: string
  thumbnail?: string
}

interface PlayerState {
  currentSong: Song | null
  isVisible: boolean
  isPlaying: boolean
  queue: Song[]
  currentIndex: number
}

const playerState = reactive<PlayerState>({
  currentSong: null,
  isVisible: false,
  isPlaying: false,
  queue: [],
  currentIndex: -1,
})

export const useYouTubePlayer = () => {
  const playSong = (song: Song) => {
    playerState.currentSong = song
    playerState.isVisible = true

    // Add to queue if not already present
    const existingIndex = playerState.queue.findIndex((s) => s.id === song.id)

    if (existingIndex === -1) {
      playerState.queue.push(song)
      playerState.currentIndex = playerState.queue.length - 1
    } else {
      playerState.currentIndex = existingIndex
    }
  }

  const addToQueue = (song: Song) => {
    const existingIndex = playerState.queue.findIndex((s) => s.id === song.id)

    if (existingIndex === -1) {
      playerState.queue.push(song)
    }
  }

  const playNext = () => {
    if (playerState.currentIndex < playerState.queue.length - 1) {
      playerState.currentIndex++
      playerState.currentSong = playerState.queue[playerState.currentIndex]
    }
  }

  const playPrevious = () => {
    if (playerState.currentIndex > 0) {
      playerState.currentIndex--
      playerState.currentSong = playerState.queue[playerState.currentIndex]
    }
  }

  const clearQueue = () => {
    playerState.queue = []
    playerState.currentIndex = -1
  }

  const removeFromQueue = (songId: string) => {
    const index = playerState.queue.findIndex((s) => s.id === songId)

    if (index !== -1) {
      playerState.queue.splice(index, 1)

      if (playerState.currentIndex >= index) {
        playerState.currentIndex--
      }
    }
  }

  const closePlayer = () => {
    playerState.isVisible = false
    playerState.currentSong = null
    playerState.isPlaying = false
  }

  const setPlaying = (playing: boolean) => {
    playerState.isPlaying = playing
  }

  onUnmounted(() => {
    closePlayer()
  })

  return {
    // State
    playerState: readonly(playerState),

    // Getters
    currentSong: computed(() => playerState.currentSong),
    isVisible: computed(() => playerState.isVisible),
    isPlaying: computed(() => playerState.isPlaying),
    queue: computed(() => playerState.queue),
    currentIndex: computed(() => playerState.currentIndex),
    hasNext: computed(() => playerState.currentIndex < playerState.queue.length - 1),
    hasPrevious: computed(() => playerState.currentIndex > 0),

    // Actions
    playSong,
    addToQueue,
    playNext,
    playPrevious,
    clearQueue,
    removeFromQueue,
    closePlayer,
    setPlaying,
  }
}
