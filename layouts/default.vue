<template>
  <div class="min-h-screen">
    <!-- Header -->
    <header class="text-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex min-h-18 flex-row items-center justify-between gap-4 py-4">
          <NuxtLink
            class="flex items-center gap-4"
            to="/"
          >
            <img
              src="/logo.png"
              class="h-[40px] min-h-[40px]"
              alt=""
            />
            <p class="hidden text-2xl font-bold md:block">
              Mhalong chords
            </p>
          </NuxtLink>
          <Input
            v-if="$route.path !== '/' && $route.path !== '/songs'"
            v-model="searchQuery"
            size="lg"
            type="text"
            leading-icon="lucide:search"
            inputmode="search"
            placeholder="ค้นหาเพลง หรือ คอร์ด..."
            class="w-full md:w-auto"
            @focus="selectAllText"
            @keyup.enter="searchSongs"
          />
        </div>
      </div>
    </header>
    <div class="mx-auto max-w-7xl px-2 pt-8 pb-[120px] sm:px-6 lg:px-8">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
const searchQuery = ref('')

const searchSongs = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/songs?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

const selectAllText = (event: Event) => {
  const target = event.target as HTMLInputElement

  target.select()
}
</script>
